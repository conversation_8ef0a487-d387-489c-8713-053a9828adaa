package UI.bag
{
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import UI.base.grid.NormalGrid;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.NumberMethod;
   import dataAll._app.achieve.AchieveData;
   import dataAll._app.active.ActiveTaskData;
   import dataAll._app.active.define.ActiveTaskDefine;
   import dataAll._app.arena.record.ArenaRecordBarData;
   import dataAll._app.ask.AskPropsData;
   import dataAll._app.blackMarket.BlackMarketCtrl;
   import dataAll._app.book.IO_BookDefine;
   import dataAll._app.city.dress.CityDressMould;
   import dataAll._app.edit.card.BCardPKCreator;
   import dataAll._app.edit.card.BossCardData;
   import dataAll._app.food.FoodRawDefine;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._app.head.HeadTempData;
   import dataAll._app.head.define.HeadDefine;
   import dataAll._app.login.SaveBaseData4399;
   import dataAll._app.outfit.OutfitData;
   import dataAll._app.parts.PartsCreator;
   import dataAll._app.parts.define.PartsConst;
   import dataAll._app.parts.define.PartsType;
   import dataAll._app.setting.key.KeyActionData;
   import dataAll._app.task.TaskData;
   import dataAll._app.task.define.TaskDefine;
   import dataAll._app.union.task.UnionTaskData;
   import dataAll._app.union.task.UnionTaskDefine;
   import dataAll._player.PlayerData;
   import dataAll._player.more.MoreData;
   import dataAll._player.more.NormalPlayerData;
   import dataAll._player.more.creator.MoreAddData;
   import dataAll._player.role.RoleName;
   import dataAll.arms.ArmsData;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.vehicle.VehicleDefine;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftBase;
   import dataAll.gift.define.GiftType;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.creator.OneProData;
   import dataAll.items.save.ItemsSave;
   import dataAll.must.define.MustDefine;
   import dataAll.pet.PetData;
   import dataAll.pet.gene.GeneData;
   import dataAll.pet.gene.creator.GeneDataCreator;
   import dataAll.pet.gene.define.GeneDefine;
   import dataAll.pet.gene.save.GeneSave;
   import dataAll.skill.HeroSkillData;
   import dataAll.skill.define.HeroSkillDefine;
   import dataAll.skill.save.HeroSkillSave;
   import dataAll.things.ThingsData;
   import dataAll.things.define.ThingsDefine;
   import dataAll.things.save.ThingsSave;
   import dataAll.ui.bar.NormalBarData;
   import flash.display.Sprite;
   import gameAll.more.DoubleCtrl;
   
   public class ItemsGrid extends NormalGrid
   {
      
      public var equipType:String = "";
      
      private var shopItemsName:String = "";
      
      public function ItemsGrid()
      {
         super();
      }
      
      public function inData_string(str0:String) : void
      {
         setName(str0);
         label = str0;
      }
      
      public function inDataByAllItems(da0:IO_ItemsData) : void
      {
         if(da0 is EquipData)
         {
            this.inData_equip(da0 as EquipData);
         }
         else if(da0 is ArmsData)
         {
            this.inData_arms(da0 as ArmsData);
         }
         else if(da0 is ThingsData)
         {
            this.inData_things(da0 as ThingsData);
         }
         else if(da0 is HeroSkillData)
         {
            this.inData_skill(da0);
         }
      }
      
      public function inData_byData(da0:IO_ItemsData) : void
      {
         var save0:ItemsSave = da0.getSave();
         setState("fill");
         setBackLabel(save0.color);
         if(save0.color == EquipColor.BLACK)
         {
            setIconLight(3618719,2,20);
         }
         else if(save0.color == EquipColor.DARKGOLD)
         {
            setIconLight(11645184,2,20);
         }
         else if(save0.color == EquipColor.PURGOLD)
         {
            setIconLight(15149442,2,20);
         }
         else if(save0.color == EquipColor.YAGOLD)
         {
            setIconLight(65460,2,20);
         }
         this.inData_items(da0);
      }
      
      private function inData_normalDefine(d0:Object) : void
      {
         setState("fill");
         if(d0.hasOwnProperty("getIconImgUrl"))
         {
            setIconName(d0.getIconImgUrl(iconMaxWidth,iconMaxHeight));
         }
         else
         {
            setIconName(d0.iconUrl);
         }
         setName(d0.cnName);
         itemsData = d0;
      }
      
      private function inData_items(da0:IO_ItemsData, iconUrl0:String = "") : void
      {
         var save0:ItemsSave = da0.getSave();
         setName(save0.getColorCnName());
         if(iconUrl0 == "")
         {
            iconUrl0 = da0.getIconImgUrl(iconMaxWidth,iconMaxHeight);
         }
         setIconName(iconUrl0);
         setNew(da0.newB);
         setSmallIcon("");
         if(da0.newB)
         {
            setSmallIcon("new");
         }
         else if(save0.getLockB())
         {
            setSmallIcon("lock");
         }
         iconReturn();
         itemsData = da0;
      }
      
      public function lightByItemsColor(size0:int = 5) : void
      {
         var color0:String = null;
         var colorValue0:uint = 0;
         if(itemsData is IO_ItemsData)
         {
            color0 = (itemsData as IO_ItemsData).getColor();
            colorValue0 = EquipColor.color(color0);
            setIconLight(colorValue0,size0);
         }
      }
      
      public function inData_arms(da0:IO_ItemsData) : void
      {
         var pd0:NormalPlayerData = null;
         this.inData_byData(da0);
         setLevelText(da0.getSave().getTrueLevel() + "");
         var arms_da0:ArmsData = da0 as ArmsData;
         if(this.width >= 149)
         {
            setShopBtnBackMc(arms_da0.getPartsUIIconLabel());
         }
         setStarIcon(arms_da0.save.getStarNum());
         setSecMc("");
         if(da0.getPlaceType() == ItemsDataGroup.PLACE_WEAR)
         {
            if(arms_da0.save.firstChoiceB)
            {
               pd0 = da0.getPlayerData();
               if(Boolean(pd0))
               {
                  setSecMc("choose");
               }
            }
         }
      }
      
      public function inData_equip(da0:IO_ItemsData) : void
      {
         this.inData_byData(da0);
         var eDa0:EquipData = da0 as EquipData;
         setStarIcon(eDa0.getStartLv());
         if(eDa0.save.partType != "fashion")
         {
            setLevelText(da0.getSave().getTrueLevel() + "");
         }
         if(eDa0.isConverB)
         {
            setNumText(ComMethod.color("转化后","#FFFF00"));
         }
         if(eDa0.getFashionShowRoleName() == "")
         {
            setShopBtnBackMc("");
         }
         else
         {
            setShopBtnBackMc("parts");
         }
         if(eDa0.isCanNumSwapB() && da0.getPlaceType() != "wear")
         {
            setNumText(eDa0.getNowNum() + "");
         }
      }
      
      public function inData_singleEquipDefine(d0:EquipDefine) : void
      {
         setIconName(d0.iconLabel);
         iconReturn();
         itemsData = d0;
      }
      
      public function inData_vehicleDefine(d0:VehicleDefine) : void
      {
         setIconName(d0.iconLabel);
         iconReturn();
         itemsData = d0;
      }
      
      public function inData_numEquipDefine(d0:EquipDefine) : void
      {
         setIconName(d0.iconLabel);
         if(d0.hasOwnProperty("lv"))
         {
            setLevelText(d0["lv"] + "");
         }
         iconReturn();
         itemsData = d0;
      }
      
      public function inData_things(da0:IO_ItemsData, showNumB0:Boolean = true) : void
      {
         var save0:ThingsSave = da0.getSave() as ThingsSave;
         setState("fill");
         if(showNumB0)
         {
            setNumText(save0.nowNum + "");
         }
         if(Boolean(save0.getDefine()))
         {
            this.inData_items(da0);
            if(save0.getDefine().maxLevel > 0)
            {
               setLevelText(save0.getTrueLevel() + "");
            }
         }
         else
         {
            setLevelText("error");
         }
         setShopBtnBackMc(save0.autoUseB ? "autoUse" : "");
      }
      
      public function inData_parts(da0:IO_ItemsData, showNumB0:Boolean = true, beforeIconB0:Boolean = false) : void
      {
         var t_da0:ThingsData = da0 as ThingsData;
         var save0:ThingsSave = da0.getSave() as ThingsSave;
         var d0:ThingsDefine = save0.getDefine();
         var lv0:int = t_da0.save.getTrueLevel();
         var iconUrl0:String = "";
         if(beforeIconB0 && PartsType.isNormalTypeB(this.equipType))
         {
            iconUrl0 = PartsConst.getIconUrl(this.equipType,lv0,true);
         }
         setState("fill");
         this.inData_items(da0,iconUrl0);
         setLevelText(String(t_da0.save.getTrueLevel()));
         if(!showNumB0)
         {
            setNumText("");
         }
         else
         {
            setNumText(save0.nowNum + "");
         }
         setBackLabel(t_da0.save.getPartsColor());
      }
      
      public function inData_thingsDefine(d0:ThingsDefine) : void
      {
         if(d0.father == "parts")
         {
            this.inData_parts(PartsCreator.getThingsDataByPartsDefine(d0));
         }
         else
         {
            this.inData_normalDefine(d0);
         }
      }
      
      public function inData_thingsDefineNum(d0:ThingsDefine) : void
      {
         var num0:int = 0;
         var pd0:PlayerData = Gaming.PG.da;
         if(d0.father == "parts")
         {
            this.inData_parts(PartsCreator.getThingsDataByPartsDefine(d0));
            num0 = pd0.partsBag.getThingsNum(d0.name);
         }
         else
         {
            this.inData_normalDefine(d0);
            num0 = pd0.thingsBag.getThingsNum(d0.name);
         }
         if(num0 > 0)
         {
            setNumText(num0 + "");
         }
         else
         {
            setNumText("");
         }
      }
      
      public function inData_thingsMust(name0:String, mustNum0:int, thingsType0:String, pd0:PlayerData, meMoreDef0:Object) : Boolean
      {
         var things_d0:ThingsDefine = null;
         var equip_d0:EquipDefine = null;
         this.shopItemsName = name0;
         var nowNum0:int = 0;
         if(thingsType0 == MustDefine.THINGS)
         {
            things_d0 = Gaming.defineGroup.things.getDefine(name0);
            nowNum0 = pd0.getThingsNum(name0);
            this.inData_thingsDefine(things_d0);
         }
         else if(thingsType0 == MustDefine.NUM_EQUIP)
         {
            nowNum0 = pd0.equipBag.getEquipNum(name0);
            equip_d0 = Gaming.defineGroup.getNumEquipDefine(name0);
            if(equip_d0 == meMoreDef0)
            {
               nowNum0--;
            }
            this.inData_numEquipDefine(equip_d0);
         }
         setMustNumText(nowNum0,mustNum0);
         return nowNum0 >= mustNum0;
      }
      
      public function inData_gameWorldProps(da0:ThingsData) : void
      {
         var d0:ThingsDefine = da0.save.getDefine();
         setLevelText(DoubleCtrl.getPropsKeyName(d0.name));
         var canUseNum0:int = Gaming.PG.da.thingsBag.getLevelCanUseNum(d0.name);
         var nowNum0:int = da0.save.nowNum;
         if(nowNum0 > canUseNum0)
         {
            nowNum0 = canUseNum0;
         }
         setNumText(nowNum0 + "");
         actived = nowNum0 > 0;
         setLockVisible(!actived);
         setIconName(da0.getIconImgUrl());
         itemsData = da0;
      }
      
      public function inData_gene(da0:IO_ItemsData, showNumB0:Boolean = true) : void
      {
         var da2:GeneData = da0 as GeneData;
         this.inData_byData(da0);
         setLevelText(da0.getSave().getTrueLevel() + "");
      }
      
      public function inData_geneDefine(d0:GeneDefine) : void
      {
         var body_d0:NormalBodyDefine = d0.getBodyDefine();
         setIconName(body_d0.headIconUrl);
         itemsData = d0;
      }
      
      public function inData_pet(da0:PetData) : void
      {
         this.inData_smallPet(da0);
         setSmallIcon(da0.getState());
      }
      
      public function inData_smallPet(da0:PetData) : void
      {
         var body_d0:NormalBodyDefine = da0.getBodyDefine();
         setName(da0.getColorPlayerName());
         setLevelText(da0.save.base.level + "");
         setPriceText(da0.gene.getPetHead());
         setIconName(body_d0.headIconUrl);
         iconReturn();
         itemsData = da0;
      }
      
      public function inData_dispatchPet(da0:PetData) : void
      {
         var body_d0:NormalBodyDefine = null;
         var s0:String = null;
         itemsData = da0;
         if(Boolean(da0))
         {
            body_d0 = da0.getBodyDefine();
            setLockVisible(false);
            setName(da0.getColorPlayerName());
            setIconName(body_d0.headIconUrl);
            s0 = "";
            s0 += "派遣时长" + da0.getDispatchHour() + "小时";
            s0 += "\n获得高级物品概率" + NumberMethod.toPer(da0.getDispatchHighPro(),0);
            if(priceTxt.defaultTextFormat.leading >= 8)
            {
               FontDeal.onlyDealLine(priceTxt);
            }
            priceTxt.text = s0;
            if(Boolean(extraBtn))
            {
               extraBtn.visible = true;
            }
         }
         else
         {
            setLockVisible(true);
            if(Boolean(extraBtn))
            {
               extraBtn.visible = false;
            }
         }
      }
      
      public function inData_skill(da0:IO_ItemsData) : void
      {
         var sda0:HeroSkillData = da0 as HeroSkillData;
         var save0:HeroSkillSave = da0.getSave() as HeroSkillSave;
         var d0:HeroSkillDefine = save0.getDefine();
         var baseD0:HeroSkillDefine = save0.getOriginalDefine();
         setState("fill");
         setMustNumText(save0.lv,baseD0.getMaxLevel(),"#00FF00","#999999");
         setShopBtnBackMc(d0.noNeedEquipB && save0.effB ? "parts" : "");
         this.inData_items(da0);
         if(sda0.canUseB() == false)
         {
            setSmallIcon("noEquip");
         }
      }
      
      public function inData_SkillDefine(d0:HeroSkillDefine) : void
      {
         this.inData_normalDefine(d0);
      }
      
      public function inData_UIPetSkill(obj0:Object) : void
      {
         var da0:HeroSkillData = null;
         var d0:HeroSkillDefine = null;
         if(obj0 is HeroSkillData)
         {
            da0 = obj0 as HeroSkillData;
            this.inData_skill(da0);
            setIconGrayFilter(false);
            if(iconMaxWidth < 50 && iconMaxWidth > 0)
            {
               setNumText(da0.save.lv + "");
            }
            setShopBtnBackMc("");
            if(da0.save.effB)
            {
               setSmallIcon("");
            }
            else
            {
               setSmallIcon("noEquip");
            }
         }
         else
         {
            d0 = obj0 as HeroSkillDefine;
            this.inData_SkillDefine(d0);
            setIconGrayFilter(true);
         }
      }
      
      public function inData_goods(da0:GoodsData) : void
      {
         this.inData_normalDefine(da0.def);
         itemsData = da0;
         setNewMc(da0.def.newB ? "new" : "");
         var num0:int = da0.def.num;
         setNumText(num0 > 1 ? "x" + num0 : "");
      }
      
      public function inData_GoodsDefine(d0:GoodsDefine) : void
      {
         this.inData_normalDefine(d0);
      }
      
      public function inData_gift(d0:GiftAddDefine) : void
      {
         var t_d0:ThingsDefine = null;
         var equipData0:EquipData = null;
         var armsData0:ArmsData = null;
         var frd0:FoodRawDefine = null;
         var bossCardDa0:BossCardData = null;
         this.shopItemsName = d0.name;
         if(d0.type == "things" || d0.type == "parts")
         {
            t_d0 = Gaming.defineGroup.things.getDefine(d0.name);
            this.inData_thingsDefine(t_d0);
            setNumText(d0.num + "");
         }
         else if(d0.type == "base")
         {
            this.inData_baseGift(d0);
         }
         else if(d0.type == "equip")
         {
            if(Boolean(d0.itemsSave))
            {
               this.inData_equip(d0.itemsSave.getSimulateData(Gaming.PG.da));
            }
            else
            {
               equipData0 = Gaming.defineGroup.equipCreator.getTempDataByName(d0.name);
               if(Boolean(equipData0))
               {
                  equipData0.save.nowNum = d0.num;
                  this.inData_equip(equipData0);
               }
            }
         }
         else if(d0.type == "arms")
         {
            if(Boolean(d0.itemsSave))
            {
               this.inData_arms(d0.itemsSave.getSimulateData(Gaming.PG.da));
            }
            else
            {
               armsData0 = Gaming.defineGroup.armsCreator.getTempDataByGift(d0);
               if(Boolean(armsData0))
               {
                  this.inData_arms(armsData0);
               }
            }
         }
         else if(d0.type == "more")
         {
            this.inData_moreAddData(new MoreAddData(d0));
         }
         else if(d0.type == "head")
         {
            this.inData_headDefine(Gaming.defineGroup.head.getDefine(d0.name));
         }
         else if(d0.type == "gene")
         {
            if(Boolean(d0.itemsSave))
            {
               this.inData_gene(GeneDataCreator.getTempData(d0.itemsSave as GeneSave));
            }
            else
            {
               this.inData_gene(GeneDataCreator.getTempDataByGift(d0));
            }
         }
         else if(d0.type == GiftType.foodRaw)
         {
            frd0 = Gaming.defineGroup.food.raw.getNormalDefine(d0.name) as FoodRawDefine;
            setIconName(frd0.iconUrl);
            setNumText(d0.num + "");
         }
         else if(d0.type == GiftType.bossCard)
         {
            bossCardDa0 = BCardPKCreator.getData(d0.name);
            this.inData_bossCardData(bossCardDa0);
         }
      }
      
      public function inData_giftSec(d0:GiftAddDefine) : void
      {
         this.inData_gift(d0);
         secData = d0;
      }
      
      override public function getShopItemsName() : String
      {
         if(this.shopItemsName != "")
         {
            return this.shopItemsName;
         }
         return super.getShopItemsName();
      }
      
      public function inData_baseGift(d0:GiftAddDefine) : void
      {
         this.shopItemsName = d0.name;
         setState("fill");
         setIconName(d0.getBaseIconUrl());
         if(GiftBase.gripNoNumArr.indexOf(d0.name) == -1)
         {
            setNumText(d0.num + "");
         }
         itemsData = d0;
      }
      
      public function inData_task(obj0:Object) : void
      {
         var diff0:int = 0;
         var da0:TaskData = obj0 as TaskData;
         var d0:TaskDefine = da0.def;
         activedAndEnabled = false;
         setShopBtnBackMc(da0.state);
         setName(da0.getTaskBarTitleText());
         itemsData = obj0;
         label = d0.name;
         actived = da0.state != "lock";
         if(actived)
         {
            diff0 = da0.getDiff();
            if(diff0 == -1)
            {
               if(da0.haveBossUpLevelB() && da0.haveDownBossUpLevelB())
               {
                  setSmallIcon("down");
               }
               else
               {
                  setSmallIcon("");
               }
            }
            else
            {
               setSmallIcon("diff_" + diff0);
            }
         }
         else
         {
            setSmallIcon("");
         }
      }
      
      public function inData_SaveListDefine(d0:SaveBaseData4399) : void
      {
         var str0:String = RoleName.clearRoleName(d0.title) + "\n" + ComMethod.color(d0.datetime,"#999999");
         setName(str0,true,true);
         itemsData = d0;
         setNew(true);
         setAnnotationVisible(false);
         setSmallIcon(d0.status == "0" ? "" : "lock");
         setNumText(d0.getWinRoleStr());
      }
      
      public function noData_SaveListDefine(annotation0:String) : void
      {
         setName("");
         itemsData = null;
         setNew(false);
         setAnnotationVisible(true);
         setAnnotation(annotation0);
         setSmallIcon("");
         setNumText("");
      }
      
      public function inData_UIMore(da0:IO_ItemsData) : void
      {
         setState("fill");
         var da2:MoreData = da0 as MoreData;
         label = da2.def.name;
         activedAndEnabled = false;
         activedAndGray = true;
         setIconName(da2.getMoreUIIcon(),false);
         if(Boolean(da0.getSave()))
         {
            actived = true;
            getIcon().alpha = 1;
            setSmallIcon(da2.DATA.getPlayerCtrlString());
            if(da2.isFightB() == false)
            {
               this.filters = [no_filter];
            }
            else
            {
               this.filters = [];
               setNew(true);
            }
         }
         else
         {
            actived = false;
            getIcon().alpha = 0.3;
         }
         iconReturn();
         itemsData = da0;
      }
      
      public function inData_moreAddData(da0:MoreAddData) : void
      {
         setLevelText(da0.getLv() + "");
         setIconName(da0.bodyDef.headIconUrl);
         itemsData = da0;
      }
      
      public function inData_arenaRecord(da0:ArenaRecordBarData) : void
      {
         setNumText(da0.getFirst());
         setName(da0.getString());
         setPriceText(da0.time);
         setSmallIcon(da0.canShowBtnB() ? "one" : "");
         itemsData = da0;
      }
      
      public function inData_blackMarket(da0:IO_ItemsData) : void
      {
         var save0:ItemsSave = da0.getSave();
         setState("fill");
         setLevelText(save0.getTrueLevel() + "");
         setPriceText(BlackMarketCtrl.getPrice(da0) + "");
         if(da0 is ArmsData)
         {
            setOtherText((da0 as ArmsData).getUIShowDps() + "");
         }
         else
         {
            setOtherText("");
         }
         setBackLabel(da0.getColor());
         this.inData_items(da0);
      }
      
      public function inData_medel(da0:AchieveData) : void
      {
         setIconName(da0.getIconUrl());
         setName(da0.getCnName());
         setNew(da0.newB);
         itemsData = da0;
         actived = true;
      }
      
      public function inData_noMedel() : void
      {
         actived = false;
      }
      
      public function inData_proData(da0:OneProData) : void
      {
         setSmallIcon(da0.type);
         setName(ComMethod.color(da0.cnName,da0.type == "skill" ? "#00FFFF" : "#66FF00"));
         setNumText(da0.valueString);
         setShopBtnBackMc(da0.lockB ? "unlock" : "lock");
         itemsData = da0;
      }
      
      public function inData_barData(da0:NormalBarData) : void
      {
         setName(da0.text);
         setOtherText(da0.otherText);
         label = da0.label;
         itemsData = da0;
      }
      
      public function inData_askProps(da0:AskPropsData) : void
      {
         itemsData = da0;
         setName(da0.def.cnName);
         setSmallIcon(da0.def.name);
         var num0:int = da0.getCanUseNum();
         setNumText(num0 + "");
         actived = num0 > 0;
      }
      
      public function inData_unionTask(da0:UnionTaskData) : void
      {
         var d0:UnionTaskDefine = da0.def;
         itemsData = da0;
         setName(d0.cnName);
         setOtherText(d0.info);
         setNumText(d0.contribution + "");
         setLevelText(da0.getProcessStr());
         var btn0:NormalBtn = extraBtn;
         if(Boolean(btn0))
         {
            btn0.actived = da0.canGetB();
            if(da0.save.getB)
            {
               btn0.setName("已领取");
               btn0.alpha = 1;
            }
            else
            {
               btn0.alpha = da0.isCompleteB() ? 1 : 0;
               btn0.setName("领取贡献");
            }
         }
      }
      
      public function inData_keyAction(da0:KeyActionData) : void
      {
         label = da0.name;
         itemsData = da0;
         var str0:String = da0.getString();
         if(str0 == "无")
         {
            str0 = ComMethod.color(str0,"#999999");
         }
         setName(str0);
         actived = da0.getBtnActived();
      }
      
      public function inData_byActiveTaskData(da0:ActiveTaskData) : void
      {
         var d0:ActiveTaskDefine = da0.def;
         var bb0:Boolean = da0.getCompleteB();
         var noGotoB0:Boolean = d0.noGotoB;
         setName(ComMethod.color(d0.cnName,bb0 ? "#00FF00" : "CCCCCC"));
         setMustNumText(da0.now,d0.num);
         setLevelText(ComMethod.color("+" + d0.active,bb0 ? "#00FF00" : "CCCCCC"));
         setNew(bb0);
         if(d0.name.indexOf("task") >= 0 || d0.name.indexOf("Task") >= 0)
         {
            if(!Gaming.uiGroup.mainUI.getBtn("task").actived)
            {
               noGotoB0 = false;
            }
         }
         if(!bb0 && !noGotoB0)
         {
            setSmallIcon("goto");
            actived = true;
         }
         else
         {
            setSmallIcon("");
            actived = false;
         }
         itemsData = da0;
      }
      
      public function inData_headTempData(da0:HeadTempData) : void
      {
         itemsData = da0;
         var d0:HeadDefine = da0.define;
         setIconName(d0.iconUrl);
         if(!(Gaming.swfLoaderManager.getResourceFull(d0.iconUrl) is Sprite))
         {
            setName(d0.cnName);
         }
         var unlockLv0:int = d0.unlockLv;
         setLevelText(unlockLv0 > 0 ? unlockLv0 + "" : "");
         if(da0.isNowB)
         {
            setSmallIcon("now");
         }
         else if(d0.noEquip)
         {
            setSmallIcon("noEquip");
         }
         else
         {
            setSmallIcon("");
         }
         setLockVisible(da0.lockB);
         label = d0.name;
      }
      
      public function inData_headDefine(d0:HeadDefine) : void
      {
         itemsData = d0;
         setIconName(d0.getIconUrl48());
         label = d0.name;
      }
      
      public function inData_outfitData(da0:OutfitData) : void
      {
         itemsData = da0;
         label = da0.define.name;
         setSmallIcon(da0.haveB ? "have" : "");
         setIconName(da0.define.iconUrl);
      }
      
      override public function clearData() : void
      {
         super.clearData();
         this.shopItemsName = "";
      }
      
      public function inData_dressMould(m0:CityDressMould) : void
      {
         itemsData = m0;
         setIconName(m0.getIconUrl());
         setNumText(m0.getUseSpace() + "");
      }
      
      public function inData_bookDefine(d0:IO_BookDefine) : void
      {
         itemsData = d0;
         setIconName(d0.getBookIconUrl(iconMaxWidth,iconMaxHeight));
         mouseIconEffectB = true;
         if(Gaming.PG.da.getNameNum(d0.getName()) > 0)
         {
            setSmallIcon("have");
            setName(ComMethod.color(d0.getCnName(),"#FF6600"));
         }
         else
         {
            setSmallIcon("");
            setName(d0.getCnName());
         }
      }
      
      public function inData_bossCardData(da0:BossCardData) : void
      {
         itemsData = da0;
         setLevel(da0.getStar());
         setIconName(da0.getIconUrl());
         setStarIcon(da0.getStar());
      }
      
      public function setImgToEquipGrip() : void
      {
         setImg(Gaming.uiGroup.getBasicMovieClip("equipGrip"));
      }
      
      public function setImgToArmsGrip() : void
      {
         setImg(Gaming.uiGroup.getBasicMovieClip("armsGrip"));
      }
      
      public function setImgToSmallThingsGrip() : void
      {
         setImg(Gaming.uiGroup.getBasicMovieClip("smallThingsGrip"));
      }
      
      override public function set visible(bb0:Boolean) : void
      {
         var da0:IO_ItemsData = null;
         super.visible = bb0;
         if(Boolean(itemsData))
         {
            da0 = itemsData as IO_ItemsData;
            if(Boolean(da0))
            {
               if(Boolean(da0.getSave()))
               {
                  da0.newB = false;
               }
            }
         }
      }
   }
}

